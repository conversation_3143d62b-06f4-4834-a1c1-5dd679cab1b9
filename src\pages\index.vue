<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import AnalyticsCount from '@/components/analytics/AnalyticsCount.vue'
import AnalyticsProjectTable from '@/components/analytics/AnalyticsProjectTable.vue'
import AnalyticsSupportTracker from '@/components/analytics/AnalyticsSupportTracker.vue'
import ApexChartAreaChart from '@/components/analytics/ApexChartAreaChart.vue'

const { t } = useI18n()
</script>

<template>
  <VRow class="match-height">
    <VCol cols="12">
      <AnalyticsCount />
    </VCol>
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardItem class="d-flex flex-wrap justify-space-between gap-4">
          <VCardTitle>{{ t('BandwidthUsage') }}</VCardTitle>
          <VCardSubtitle>{{ t('TotalUsageAllDevices') }}</VCardSubtitle>
          <!--
            <template #append>
            <div class="date-picker-wrapper">
            <AppDateTimePicker
            model-value="2022-06-09"
            prepend-inner-icon="tabler-calendar"
            placeholder="Select Date"
            :config="$vuetify.display.smAndDown ? { position: 'auto center' } : { position: 'auto right' }"
            />
            </div>
            </template>
          -->
        </VCardItem>
        <VCardText>
          <ApexChartAreaChart />
        </VCardText>
      </VCard>
    </VCol>

    <VCol
      cols="12"
      md="6"
    >
      <AnalyticsSupportTracker />
    </VCol>

    <VCol cols="12">
      <AnalyticsProjectTable />
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/libs/apex-chart";
</style>
