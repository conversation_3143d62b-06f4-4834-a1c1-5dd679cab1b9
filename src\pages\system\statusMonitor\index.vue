<script lang="ts" setup>
import { isEqual } from 'lodash-es'
import { onMounted, onUnmounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import AcCpu from '@/components/system/statusMonitor/acCpu.vue'
import AcInfoBox from '@/components/system/statusMonitor/acInfo.vue'
import AcMemory from '@/components/system/statusMonitor/acMemory.vue'
import AcNetwork from '@/components/system/statusMonitor/acNetwork.vue'

const { t } = useI18n()

const mainList = ref([])
const cpuList = ref([])
const memoryList = ref([])

// 定时器引用 - 使用更明确的类型
let refreshTimer: NodeJS.Timeout | null = null

// 页面可见性状态
const isPageVisible = ref(true)

// 启动定时刷新
const startAutoRefresh = () => {
  // 清除已存在的定时器
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }

  // 设置15秒定时刷新
  refreshTimer = setInterval(() => {
    // 只在页面可见时刷新数据
    if (isPageVisible.value)
      getDataList()
  }, 15000)
}

// 停止定时刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 页面可见性变化处理
const handleVisibilityChange = () => {
  isPageVisible.value = !document.hidden

  if (isPageVisible.value) {
    // 页面变为可见时，立即刷新一次数据并重启定时器
    getDataList()
    startAutoRefresh()
  }
  else {
    // 页面不可见时，停止定时器节省资源
    stopAutoRefresh()
  }
}

onMounted(() => {
  // 初始加载数据
  getDataList()

  // 启动定时刷新
  startAutoRefresh()

  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  // 清理定时器
  stopAutoRefresh()

  // 移除事件监听器
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

const num = ref(0)

// 存储上一次数据的引用
const prevData = ref({
  cpu: [],
  memory: [],
  main: [],
})

// 请求失败重试计数
let retryCount = 0
const maxRetries = 3

// 加载状态
const isLoading = ref(false)

// 子组件引用
const acInfoRef = ref()

async function getDataList() {
  // 防止重复请求
  if (isLoading.value)
    return

  isLoading.value = true

  try {
    const data = await $api('', { requestType: 221 })

    if (data?.err_code !== 0 || !data?.info) {
      console.error('API Error:', data?.err_msg || 'Unknown error')

      // 重试逻辑
      if (retryCount < maxRetries) {
        retryCount++
        console.warn(`请求失败，正在进行第 ${retryCount} 次重试...`)
        setTimeout(() => {
          getDataList()
        }, 2000) // 2秒后重试
      }
      else {
        console.error('达到最大重试次数，停止重试')
        retryCount = 0 // 重置重试计数
      }

      return
    }

    // 请求成功，重置重试计数
    retryCount = 0

    const { system, network } = data.info
    const newCpu = system?.cpu_history || []
    const newMemory = system?.memory_history || []
    const newMain = network?.br_wan?.rate_history || []

    // 深度对比三个核心数据字段
    const isDataChanged
      = !isEqual(newCpu, prevData.value.cpu)
      || !isEqual(newMemory, prevData.value.memory)
      || !isEqual(newMain, prevData.value.main)

    if (isDataChanged) {
      // 更新视图数据
      cpuList.value = newCpu
      memoryList.value = newMemory
      mainList.value = newMain

      // 存储当前数据用于下次对比 - 使用 structuredClone 替代 JSON 方法（更高效）
      prevData.value = {
        cpu: structuredClone(newCpu),
        memory: structuredClone(newMemory),
        main: structuredClone(newMain),
      }

      // 触发计数变更
      num.value++
    }

    // 同时刷新 AcInfoBox 组件的数据
    if (acInfoRef.value?.refreshData)
      await acInfoRef.value.refreshData()
  }
  catch (error) {
    console.error('请求异常:', error)

    // 网络错误也进行重试
    if (retryCount < maxRetries) {
      retryCount++
      console.warn(`网络异常，正在进行第 ${retryCount} 次重试...`)
      setTimeout(() => {
        getDataList()
      }, 2000)
    }
    else {
      console.error('网络异常达到最大重试次数，停止重试')
      retryCount = 0
    }
  }
  finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VRow class="match-height">
    <!-- 👉 AC信息 -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardItem class="d-flex flex-wrap justify-space-between gap-4">
          <VCardTitle>{{ t('SystemStatus.Title') }}</VCardTitle>
          <VCardSubtitle>{{ t('SystemStatus.CurrentSettings') }}</VCardSubtitle>
        </VCardItem>
        <VCardText>
          <AcInfoBox ref="acInfoRef" />
        </VCardText>
      </VCard>
    </VCol>

    <!-- 👉 AC 网络流速 -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardItem class="d-flex flex-wrap justify-space-between gap-4">
          <VCardTitle>{{ t('NetworkTraffic.Title') }}</VCardTitle>
          <VCardSubtitle>{{ t('NetworkTraffic.Subtitle') }}</VCardSubtitle>
        </VCardItem>
        <VCardText>
          <AcNetwork
            :key="num + 1"
            :main-list="mainList"
          />
        </VCardText>
      </VCard>
    </VCol>

    <!-- 👉 AC CPU -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardItem class="d-flex flex-wrap justify-space-between gap-4">
          <VCardTitle>{{ t('CPUUsage.Title') }}</VCardTitle>
          <VCardSubtitle>{{ t('CPUUsage.Subtitle') }}</VCardSubtitle>
        </VCardItem>
        <VCardText>
          <AcCpu
            :key="num + 2"
            :cpu-list="cpuList"
          />
        </VCardText>
      </VCard>
    </VCol>

    <!-- 👉 AC 内存 -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardItem class="d-flex flex-wrap justify-space-between gap-4">
          <VCardTitle>{{ t('MemoryUsage.Title') }}</VCardTitle>
          <VCardSubtitle>{{ t('MemoryUsage.Subtitle') }}</VCardSubtitle>
        </VCardItem>
        <VCardText>
          <AcMemory
            :key="num + 3"
            :memory-list="memoryList"
          />
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/libs/apex-chart";
</style>
